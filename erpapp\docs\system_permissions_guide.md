# دليل نظام صلاحيات مجلد System

## نظرة عامة

تم تطوير نظام صلاحيات مبسط يسمح بالوصول لجميع صفحات مجلد `System` للمستخدمين المسجلين بدون الحاجة لشركة حالية أو صلاحيات خاصة.

## كيفية عمل النظام

### المبدأ الأساسي
- **مجلد System**: صفحات عامة للمستخدمين (لا تحتاج شركة)
- **مجلد Modules**: صفحات الأعمال (تحتاج شركة + صلاحيات)

### آلية التحقق
1. **تسجيل الدخول**: فحص أساسي لجميع الصفحات
2. **فحص مجلد System**: إذا كان المسار من System → السماح مباشرة
3. **فحص الشركة**: إذا لم يكن من System → التحقق من الشركة والصلاحيات

## المجلدات المسموحة حالياً

```php
$systemModules = [
    'companies',      // إدارة الشركات
    'users',          // إدارة المستخدمين  
    'subscriptions',  // الاشتراكات
    'profile',        // الملف الشخصي
    'settings',       // الإعدادات
    'dashboard',      // لوحة التحكم
    'language',       // تبديل اللغة
    'lang',           // تبديل اللغة (مسار بديل)
    'logout',         // تسجيل الخروج
];
```

## إضافة مجلد جديد

### الخطوة 1: تحديث قائمة المجلدات
في ملف `erpapp/App/Helpers/permissions.php`، ابحث عن دالة `isSystemRoute()` وأضف المجلد الجديد:

```php
$systemModules = [
    'companies',
    'users',
    'subscriptions',
    'profile',
    'settings',
    'dashboard',
    'language',
    'lang',
    'logout',
    'notifications',  // مجلد جديد
    'reports',        // مجلد جديد آخر
];
```

### الخطوة 2: إنشاء المجلد والملفات
```
erpapp/App/System/Notifications/
├── Controllers/
│   └── NotificationController.php
├── Views/
│   └── index.php
├── Models/
│   └── Notification.php
└── Module.php
```

### الخطوة 3: تسجيل المسارات
في ملف `Module.php`:

```php
<?php
namespace App\System\Notifications;

use App\Core\Module as BaseModule;

class Module extends BaseModule
{
    public function registerRoutes()
    {
        add_route('GET', '/notifications', 'App\System\Notifications\Controllers\NotificationController@index');
        add_route('GET', '/notifications/{id}', 'App\System\Notifications\Controllers\NotificationController@show');
        // المزيد من المسارات...
    }
}
```

## أمثلة على المسارات المسموحة

### مسارات مباشرة:
- ✅ `/companies`
- ✅ `/users`
- ✅ `/profile`
- ✅ `/settings`
- ✅ `/logout`

### مسارات فرعية:
- ✅ `/companies/create`
- ✅ `/companies/123/edit`
- ✅ `/users/profile`
- ✅ `/settings/theme/dark`
- ✅ `/language/ar`

### مسارات غير مسموحة (تحتاج شركة):
- ❌ `/inventory` (من مجلد Modules)
- ❌ `/sales` (من مجلد Modules)
- ❌ `/accounting` (من مجلد Modules)

## المسارات الخاصة

يتم التعامل مع بعض المسارات بشكل خاص:

```php
$specialRoutes = [
    '/^language\/(ar|en)$/',                    // language/ar, language/en
    '/^lang\/(ar|en)$/',                        // lang/ar, lang/en
    '/^settings\/(theme|sidebar|content)\//',   // settings/theme/*, settings/sidebar/*
    '/^settings\/sound-notifications\//',       // settings/sound-notifications/*
];
```

## اختبار النظام

### للتأكد من عمل النظام:

1. **سجل دخول مستخدم بدون شركة**
2. **جرب الوصول للمسارات التالية:**
   - ✅ `/companies` → يجب أن يعمل
   - ✅ `/profile` → يجب أن يعمل
   - ✅ `/settings` → يجب أن يعمل
   - ✅ `/language/ar` → يجب أن يعمل
   - ❌ `/inventory` → يجب أن يُمنع

## استكشاف الأخطاء

### إذا لم يعمل مجلد جديد:
1. تأكد من إضافة اسم المجلد لقائمة `$systemModules`
2. تأكد من تسجيل المسارات في `Module.php`
3. تأكد من وجود المتحكم والملفات المطلوبة
4. امسح cache التطبيق إن وجد

### إذا ظهر خطأ صلاحيات:
1. تحقق من تسجيل الدخول
2. تحقق من كتابة اسم المجلد بشكل صحيح
3. تحقق من سجلات الأخطاء

## مزايا النظام الجديد

### 1. البساطة
- إضافة مجلد واحد فقط بدلاً من كتابة كل مسار
- سهولة الصيانة والتطوير

### 2. المرونة  
- يمكن إضافة مجلدات جديدة بسهولة
- يدعم المسارات المعقدة والمعاملات

### 3. الأمان
- فصل واضح بين صفحات System و Modules
- حماية تلقائية للصفحات الحساسة

### 4. الأداء
- فحص سريع وفعال
- تقليل عدد الاستعلامات

## أفضل الممارسات

### 1. تسمية المجلدات
- استخدم أسماء واضحة ومعبرة
- تجنب الأسماء المتشابهة مع مجلدات Modules

### 2. تنظيم الملفات
- اتبع نفس هيكل مجلدات System الموجودة
- ضع كل مجلد في مكانه الصحيح

### 3. التوثيق
- وثق أي مجلد جديد تضيفه
- اشرح الغرض من كل مجلد

### 4. الاختبار
- اختبر المجلدات الجديدة قبل النشر
- تأكد من عدم تأثيرها على المجلدات الأخرى

## خلاصة

النظام الجديد يوفر طريقة بسيطة ومرنة لإدارة صلاحيات مجلد System. بدلاً من كتابة كل مسار منفرد، يمكنك الآن إضافة مجلد كامل بسطر واحد فقط!
