# دليل نظام صلاحيات مجلد System التلقائي

## نظرة عامة

تم تطوير نظام صلاحيات **تلقائي بالكامل** يكتشف جميع مجلدات `App/System` ويسمح بالوصول إليها للمستخدمين المسجلين بدون الحاجة لشركة حالية أو صلاحيات خاصة.

## كيفية عمل النظام

### المبدأ الأساسي
- **مجلد System**: صفحات عامة للمستخدمين (لا تحتاج شركة) - **اكتشاف تلقائي**
- **مجلد Modules**: صفحات الأعمال (تحتاج شركة + صلاحيات)

### آلية التحقق التلقائية
1. **تسجيل الدخول**: فحص أساسي لجميع الصفحات
2. **اكتشاف مجلدات System**: قراءة تلقائية لجميع مجلدات `App/System`
3. **فحص المسار**: إذا كان المسار من أي مجلد في System → السماح مباشرة
4. **فحص الشركة**: إذا لم يكن من System → التحقق من الشركة والصلاحيات

## الاكتشاف التلقائي للمجلدات

النظام الآن يقرأ مجلد `App/System` تلقائياً ويكتشف جميع المجلدات الموجودة:

```php
// النظام يفحص هذا المجلد تلقائياً
$systemPath = BASE_PATH . '/App/System';

// ويكتشف جميع المجلدات مثل:
// - Companies/
// - Users/
// - Subscriptions/
// - Profile/
// - Settings/
// - Dashboard/
// - أي مجلد جديد تضيفه!
```

## إضافة مجلد جديد - بساطة مطلقة!

### الخطوة الوحيدة المطلوبة:
**أنشئ المجلد في `App/System/` وانتهى!**

لا حاجة لتعديل أي كود أو إعدادات!

### مثال: إضافة مجلد Notifications

```bash
# أنشئ المجلد الجديد
mkdir erpapp/App/System/Notifications

# أنشئ الهيكل الأساسي
mkdir erpapp/App/System/Notifications/Controllers
mkdir erpapp/App/System/Notifications/Views
mkdir erpapp/App/System/Notifications/Models
```

```
erpapp/App/System/Notifications/
├── Controllers/
│   └── NotificationController.php
├── Views/
│   └── index.php
├── Models/
│   └── Notification.php
└── Module.php
```

**وانتهى! النظام سيكتشف المجلد تلقائياً ويسمح بجميع مساراته.**

### تسجيل المسارات (اختياري)
في ملف `Module.php`:

```php
<?php
namespace App\System\Notifications;

use App\Core\Module as BaseModule;

class Module extends BaseModule
{
    public function registerRoutes()
    {
        add_route('GET', '/notifications', 'App\System\Notifications\Controllers\NotificationController@index');
        add_route('GET', '/notifications/{id}', 'App\System\Notifications\Controllers\NotificationController@show');
        // المزيد من المسارات...
    }
}
```

## أمثلة على المسارات المسموحة تلقائياً

### مسارات مباشرة (حسب المجلدات الموجودة):
- ✅ `/companies` (إذا كان مجلد Companies موجود)
- ✅ `/users` (إذا كان مجلد Users موجود)
- ✅ `/profile` (إذا كان مجلد Profile موجود)
- ✅ `/settings` (إذا كان مجلد Settings موجود)
- ✅ `/dashboard` (إذا كان مجلد Dashboard موجود)
- ✅ `/notifications` (إذا أضفت مجلد Notifications)

### مسارات فرعية (تلقائياً):
- ✅ `/companies/create`
- ✅ `/companies/123/edit`
- ✅ `/users/profile`
- ✅ `/settings/theme/dark`
- ✅ `/notifications/unread`
- ✅ أي مسار فرعي لأي مجلد في System

### مسارات خاصة إضافية:
- ✅ `/logout` (تسجيل الخروج)
- ✅ `/language/ar` (تبديل اللغة)
- ✅ `/lang/en` (تبديل اللغة)

### مسارات غير مسموحة (تحتاج شركة):
- ❌ `/inventory` (من مجلد Modules)
- ❌ `/sales` (من مجلد Modules)
- ❌ `/accounting` (من مجلد Modules)
- ❌ أي مجلد خارج System

## اختبار النظام

### اختبار تلقائي:
```bash
# شغل ملف الاختبار
php erpapp/test_system_permissions.php
```

### اختبار يدوي:
1. **سجل دخول مستخدم بدون شركة**
2. **جرب الوصول للمسارات التالية:**
   - ✅ أي مسار في مجلد System → يجب أن يعمل
   - ✅ `/language/ar` → يجب أن يعمل
   - ❌ أي مسار في مجلد Modules → يجب أن يُمنع

### إضافة مجلد جديد واختباره:
```bash
# أنشئ مجلد جديد
mkdir erpapp/App/System/TestModule

# اختبر فوراً - سيعمل تلقائياً!
# /testmodule → ✅ مسموح
# /testmodule/anything → ✅ مسموح
```

## استكشاف الأخطاء

### إذا لم يعمل مجلد جديد:
1. ✅ تأكد من وجود المجلد في `App/System/`
2. ✅ تأكد من أن اسم المجلد صحيح
3. ✅ تأكد من صلاحيات القراءة للمجلد
4. ✅ امسح cache التطبيق إن وجد

### إذا ظهر خطأ صلاحيات:
1. تحقق من تسجيل الدخول
2. تحقق من أن المسار فعلاً من مجلد System
3. شغل ملف الاختبار للتأكد
4. تحقق من سجلات الأخطاء

## مزايا النظام التلقائي الجديد

### 1. البساطة المطلقة
- **صفر تعديل كود** لإضافة مجلد جديد
- **اكتشاف تلقائي** لجميع المجلدات
- **لا حاجة لقوائم يدوية**

### 2. المرونة الكاملة
- **أي مجلد** في System يعمل تلقائياً
- **أي مسار فرعي** مسموح تلقائياً
- **يدعم المسارات المعقدة** والمعاملات

### 3. الأمان المحسن
- **فصل تلقائي** بين System و Modules
- **حماية ديناميكية** للصفحات الحساسة
- **لا مجال للنسيان** أو الأخطاء اليدوية

### 4. الأداء المحسن
- **cache ذكي** للمجلدات المكتشفة
- **فحص سريع** بدون استعلامات قاعدة بيانات
- **تحميل واحد** للقائمة في كل جلسة

## أفضل الممارسات

### 1. تسمية المجلدات
- استخدم أسماء واضحة ومعبرة
- تجنب الأسماء المتشابهة مع مجلدات Modules
- استخدم PascalCase (مثل: `Notifications`, `Reports`)

### 2. تنظيم الملفات
- اتبع نفس هيكل مجلدات System الموجودة
- ضع كل مجلد في `App/System/` مباشرة
- أنشئ المجلدات الفرعية المطلوبة (Controllers, Views, Models)

### 3. التوثيق
- وثق أي مجلد جديد تضيفه
- اشرح الغرض من كل مجلد
- حدث هذا الملف عند الحاجة

### 4. الاختبار
- استخدم ملف الاختبار التلقائي
- اختبر المجلدات الجديدة قبل النشر
- تأكد من عدم تأثيرها على المجلدات الأخرى

## خلاصة

🎉 **النظام التلقائي الجديد ثورة في البساطة!**

- ✅ **صفر كود** لإضافة مجلد جديد
- ✅ **اكتشاف تلقائي** لجميع مجلدات System
- ✅ **أمان كامل** مع مرونة مطلقة
- ✅ **أداء محسن** مع cache ذكي

**ببساطة: أنشئ مجلد في `App/System/` وسيعمل تلقائياً!**

لا حاجة لتعديل أي كود، لا حاجة لقوائم يدوية، لا حاجة لتعقيدات!

النظام يكتشف كل شيء تلقائياً ويسمح بالوصول لجميع صفحات System للمستخدمين المسجلين. 🚀
