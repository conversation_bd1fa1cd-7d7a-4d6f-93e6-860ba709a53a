<?php
/**
 * تشغيل تحديثات قاعدة البيانات
 * 
 * هذا الملف يقوم بتنفيذ التحديثات المطلوبة على قاعدة البيانات
 */

// تحميل الإعدادات
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

try {
    // الاتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    echo "🔄 بدء تحديث قاعدة البيانات...\n\n";
    
    // قراءة ملف SQL
    $sqlFile = __DIR__ . '/add_status_ar_column.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: " . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    
    if (empty($sql)) {
        throw new Exception("ملف SQL فارغ");
    }
    
    echo "📄 قراءة ملف SQL: " . basename($sqlFile) . "\n";
    
    // تقسيم الاستعلامات
    $statements = explode(';', $sql);
    $executedCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // تجاهل التعليقات والأسطر الفارغة
        if (empty($statement) || 
            strpos($statement, '--') === 0 || 
            strpos($statement, '/*') === 0) {
            continue;
        }
        
        try {
            $db->exec($statement);
            $executedCount++;
            echo "✅ تم تنفيذ الاستعلام رقم: " . $executedCount . "\n";
        } catch (PDOException $e) {
            // تجاهل أخطاء العمود الموجود مسبقاً
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "⚠️  العمود موجود مسبقاً - تم التجاهل\n";
                continue;
            }
            throw $e;
        }
    }
    
    echo "\n🎉 تم تحديث قاعدة البيانات بنجاح!\n";
    echo "📊 عدد الاستعلامات المنفذة: " . $executedCount . "\n\n";
    
    // التحقق من وجود العمود
    $checkQuery = "SELECT COUNT(*) as count 
                   FROM INFORMATION_SCHEMA.COLUMNS 
                   WHERE TABLE_SCHEMA = DATABASE() 
                   AND TABLE_NAME = 'company_users' 
                   AND COLUMN_NAME = 'status_ar'";
    
    $stmt = $db->query($checkQuery);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        echo "✅ تم التأكد من وجود عمود status_ar في جدول company_users\n";
        
        // عرض عينة من البيانات
        $sampleQuery = "SELECT id, status, status_ar, user_status FROM company_users LIMIT 5";
        $stmt = $db->query($sampleQuery);
        $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($samples)) {
            echo "\n📋 عينة من البيانات المحدثة:\n";
            echo "ID\tStatus\t\tStatus AR\tUser Status\n";
            echo "---\t------\t\t---------\t-----------\n";
            foreach ($samples as $sample) {
                echo $sample['id'] . "\t" . 
                     $sample['status'] . "\t\t" . 
                     $sample['status_ar'] . "\t" . 
                     $sample['user_status'] . "\n";
            }
        }
    } else {
        echo "❌ فشل في إضافة عمود status_ar\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في تحديث قاعدة البيانات: " . $e->getMessage() . "\n";
    echo "📍 الملف: " . $e->getFile() . "\n";
    echo "📍 السطر: " . $e->getLine() . "\n";
    exit(1);
}

echo "\n✨ انتهى التحديث بنجاح!\n";
?>
