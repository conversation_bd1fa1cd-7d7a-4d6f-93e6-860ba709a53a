<?php
/**
 * نظام الصلاحيات الموحد
 * يتم تحميله تلقائياً مع loader.php
 */

class PermissionManager
{
    private static $userPermissions = null;
    private static $routePermissions = [];
    private static $programRoutes = null;

    /**
     * تحميل مسارات البرامج من قاعدة البيانات (الهيكل الجديد)
     */
    private static function loadProgramRoutes()
    {
        if (self::$programRoutes !== null) {
            return;
        }

        $user = current_user();
        if (!$user) {
            self::$programRoutes = [];
            return;
        }

        $companyId = $user['current_company_id'];
        if (!$companyId) {
            self::$programRoutes = [];
            return;
        }

        try {
            global $db;

            // جلب البرامج من الهيكل الجديد
            $stmt = $db->prepare("
                SELECT
                    mp.program_name_en,
                    mp.program_code,
                    mp.page_url,
                    mp.program_type,
                    mp.parent_program_id,
                    sm.module_code,
                    parent.program_name_en as parent_name_en,
                    parent.program_code as parent_code
                FROM module_programs mp
                JOIN system_modules sm ON sm.module_id = mp.module_id
                LEFT JOIN module_programs parent ON parent.program_id = mp.parent_program_id
                WHERE mp.company_id = ?
                AND mp.is_active = 1
                AND mp.page_url IS NOT NULL
                AND mp.page_url != ''
                ORDER BY mp.program_type DESC, mp.display_order
            ");

            $stmt->execute([$companyId]);
            $programs = $stmt->fetchAll(PDO::FETCH_ASSOC);

            self::$programRoutes = [];
            foreach ($programs as $program) {
                $programCode = strtolower($program['program_code']);
                $moduleCode = strtolower($program['module_code']);

                // تحديد البرنامج الرئيسي للصلاحيات
                $mainProgram = $moduleCode; // استخدام كود الوحدة كأساس
                if ($program['program_type'] === 'Sub' && !empty($program['parent_code'])) {
                    $mainProgram = strtolower($program['parent_code']);
                }

                // المسار الأساسي
                if (!empty($program['page_url'])) {
                    $baseUrl = trim($program['page_url'], '/');
                    self::$programRoutes[$baseUrl] = [
                        'module' => $moduleCode,
                        'program' => $programCode,
                        'main_program' => $mainProgram,
                        'action' => 'view',
                        'program_type' => $program['program_type']
                    ];

                    // إضافة المسارات الفرعية التلقائية
                    self::addSubRoutes($baseUrl, $moduleCode, $programCode);
                }
            }

        } catch (Exception $e) {
            error_log("خطأ في تحميل مسارات البرامج: " . $e->getMessage());
            self::$programRoutes = [];
        }
    }

    /**
     * إضافة المسارات الفرعية التلقائية (الهيكل الجديد)
     */
    private static function addSubRoutes($baseUrl, $moduleCode, $programCode)
    {
        $actions = [
            'create' => 'create',
            'store' => 'create',
            'edit' => 'edit',
            'update' => 'edit',
            'delete' => 'delete',
            'destroy' => 'delete',
            'show' => 'view',
            'index' => 'view'
        ];

        foreach ($actions as $route => $action) {
            // مسارات مباشرة
            self::$programRoutes[$baseUrl . '/' . $route] = [
                'module' => $moduleCode,
                'program' => $programCode,
                'action' => $action
            ];

            // مسارات مع معرف
            self::$programRoutes[$baseUrl . '/*/'. $route] = [
                'module' => $moduleCode,
                'program' => $programCode,
                'action' => $action
            ];
        }
    }

    /**
     * التحقق من الصلاحية للمسار الحالي
     */
    public static function checkCurrentRoute()
    {
        // التحقق الأساسي من تسجيل الدخول
        $user = current_user();
        if (!$user) {
            // السماح بالوصول للصفحات العامة فقط
            $publicRoutes = ['login', 'register', 'forgot-password', 'reset-password', '', 'home'];
            $currentRoute = self::getCurrentRoute();

            if (!in_array($currentRoute, $publicRoutes)) {
                self::handleNoPermission();
            }
            return;
        }

        // التحقق من وجود شركة حالية
        $companyId = $user['current_company_id'];
        if (!$companyId) {
            // السماح بصفحات اختيار الشركة فقط
            $companySelectionRoutes = ['companies/select', 'companies/join', 'companies/create', 'profile', 'logout'];
            $currentRoute = self::getCurrentRoute();

            if (!in_array($currentRoute, $companySelectionRoutes)) {
                self::handleNoPermission();
            }
            return;
        }

        // التحقق من أن المستخدم مضاف في الشركة
        if (!self::isCompanyOwner($user['UserID'], $companyId) && !self::isUserInCompany($user['UserID'], $companyId)) {
            self::handleNoPermission();
            return;
        }

        // تحميل مسارات البرامج
        self::loadProgramRoutes();

        $currentRoute = self::getCurrentRoute();

        // البحث عن تطابق مباشر
        if (isset(self::$programRoutes[$currentRoute])) {
            $routeInfo = self::$programRoutes[$currentRoute];

            // التحقق من وجود الوحدة للشركة
            if (!self::isModuleInstalledForCompany($routeInfo['module'], $companyId)) {
                self::handleModuleNotInstalled($routeInfo['module']);
                return;
            }

            // التحقق من الصلاحية
            if (!self::hasPermissionForProgram($routeInfo['program'], $routeInfo['action'])) {
                self::handleNoPermission();
            }
            return;
        }

        // البحث عن تطابق مع أنماط wildcard
        foreach (self::$programRoutes as $pattern => $routeInfo) {
            if (strpos($pattern, '*') !== false) {
                $regex = str_replace('*', '[^/]+', $pattern);
                $regex = '#^' . $regex . '$#';

                if (preg_match($regex, $currentRoute)) {
                    // التحقق من وجود الوحدة للشركة
                    if (!self::isModuleInstalledForCompany($routeInfo['module'], $companyId)) {
                        self::handleModuleNotInstalled($routeInfo['module']);
                        return;
                    }

                    // التحقق من الصلاحية
                    if (!self::hasPermissionForProgram($routeInfo['program'], $routeInfo['action'])) {
                        self::handleNoPermission();
                    }
                    return;
                }
            }
        }
    }

    /**
     * التحقق من أن الوحدة منزلة للشركة مع فحص الاشتراك (الهيكل الجديد)
     */
    private static function isModuleInstalledForCompany($moduleCode, $companyId)
    {
        try {
            global $db;

            // أولاً: التحقق من حالة الشركة والاشتراك
            if (!self::isCompanySubscriptionValid($companyId)) {
                return false;
            }

            // ثانياً: التحقق من أن الوحدة مسموحة في خطة الاشتراك
            if (!self::isModuleAllowedInSubscription($moduleCode, $companyId)) {
                return false;
            }

            // ثالثاً: التحقق من تنزيل الوحدة
            $stmt = $db->prepare("
                SELECT COUNT(*) as module_count
                FROM company_modules cm
                JOIN system_modules sm ON sm.module_id = cm.module_id
                WHERE cm.company_id = ?
                AND sm.module_code = ?
                AND cm.is_active = 1
                AND (cm.license_expires_at IS NULL OR cm.license_expires_at > NOW())
            ");

            $stmt->execute([$companyId, $moduleCode]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result['module_count'] > 0;

        } catch (Exception $e) {
            error_log("خطأ في التحقق من تنزيل الوحدة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من صحة اشتراك الشركة
     */
    private static function isCompanySubscriptionValid($companyId)
    {
        try {
            global $db;

            // التحقق من حالة الشركة
            $stmt = $db->prepare("
                SELECT
                    c.CompanyStatus,
                    c.subscription_id,
                    c.trial_end_date,
                    s.status as subscription_status,
                    s.end_date as subscription_end_date
                FROM companies c
                LEFT JOIN subscriptions s ON s.subscription_id = c.subscription_id
                WHERE c.CompanyID = ?
            ");

            $stmt->execute([$companyId]);
            $company = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$company) {
                return false;
            }

            // التحقق من حالة الشركة
            if ($company['CompanyStatus'] === 'Inactive' || $company['CompanyStatus'] === 'Expired') {
                return false;
            }

            // إذا كانت الشركة في فترة تجريبية
            if ($company['CompanyStatus'] === 'Trial') {
                if ($company['trial_end_date'] && strtotime($company['trial_end_date']) < time()) {
                    return false; // انتهت الفترة التجريبية
                }
                return true; // الفترة التجريبية سارية
            }

            // إذا كانت الشركة نشطة، التحقق من الاشتراك
            if ($company['CompanyStatus'] === 'Active') {
                if (!$company['subscription_id']) {
                    return false; // لا يوجد اشتراك
                }

                if ($company['subscription_status'] !== 'active') {
                    return false; // الاشتراك غير نشط
                }

                if ($company['subscription_end_date'] && strtotime($company['subscription_end_date']) < time()) {
                    return false; // انتهى الاشتراك
                }

                return true; // الاشتراك سار
            }

            return false;

        } catch (Exception $e) {
            error_log("خطأ في التحقق من صحة اشتراك الشركة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من أن الوحدة مسموحة في خطة الاشتراك
     */
    private static function isModuleAllowedInSubscription($moduleCode, $companyId)
    {
        try {
            global $db;

            // الحصول على معلومات الشركة والاشتراك
            $stmt = $db->prepare("
                SELECT
                    c.CompanyStatus,
                    c.subscription_id,
                    s.plan_id
                FROM companies c
                LEFT JOIN subscriptions s ON s.subscription_id = c.subscription_id
                WHERE c.CompanyID = ?
            ");

            $stmt->execute([$companyId]);
            $company = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$company) {
                return false;
            }

            // إذا كانت الشركة في فترة تجريبية، السماح بجميع الوحدات الأساسية والإضافية
            if ($company['CompanyStatus'] === 'Trial') {
                $stmt = $db->prepare("
                    SELECT COUNT(*) as count
                    FROM system_modules
                    WHERE module_code = ?
                    AND module_type IN ('core', 'addon')
                ");
                $stmt->execute([$moduleCode]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                return $result['count'] > 0;
            }

            // إذا كانت الشركة نشطة، التحقق من نوع الوحدة فقط
            if ($company['CompanyStatus'] === 'Active') {
                // الحصول على نوع الوحدة
                $stmt = $db->prepare("
                    SELECT module_type
                    FROM system_modules
                    WHERE module_code = ?
                ");
                $stmt->execute([$moduleCode]);
                $moduleInfo = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$moduleInfo) {
                    return false;
                }

                // جميع الوحدات متاحة للشركات النشطة
                // يمكن تخصيص هذا المنطق حسب الحاجة
                return in_array($moduleInfo['module_type'], ['core', 'addon', 'premium']);
            }

            return false;

        } catch (Exception $e) {
            error_log("خطأ في التحقق من سماح الوحدة في الاشتراك: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من صلاحية المستخدم للبرنامج (الهيكل الجديد)
     */
    private static function hasPermissionForProgram($programCode, $action)
    {
        $user = current_user();
        if (!$user) return false;

        $companyId = $user['current_company_id'];
        if (!$companyId) return false;

        // مالك الشركة له صلاحيات كاملة
        if (self::isCompanyOwner($user['UserID'], $companyId)) {
            return true;
        }

        try {
            global $db;
            $stmt = $db->prepare("
                SELECT p.*
                FROM permissions p
                JOIN module_programs mp ON mp.program_id = p.program_id
                JOIN company_users cu ON cu.position_id = p.position_id
                WHERE cu.user_id = ?
                AND cu.company_id = ?
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
                AND mp.program_code = ?
                AND p.company_id = ?
            ");

            $stmt->execute([$user['UserID'], $companyId, $programCode, $companyId]);
            $permission = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$permission) return false;

            switch ($action) {
                case 'view': return (bool)$permission['can_view'];
                case 'create': return (bool)$permission['can_create'];
                case 'edit': return (bool)$permission['can_edit'];
                case 'delete': return (bool)$permission['can_delete'];
                case 'approve': return (bool)$permission['can_approve'];
                default: return false;
            }

        } catch (Exception $e) {
            error_log("خطأ في التحقق من صلاحية البرنامج: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التعامل مع عدم تنزيل الوحدة للشركة (الهيكل الجديد)
     */
    private static function handleModuleNotInstalled($moduleCode)
    {
        $user = current_user();
        $companyId = $user['current_company_id'] ?? null;

        // تحديد سبب عدم الوصول
        $reason = 'وحدة غير منزلة';
        $message = 'هذه الوحدة غير منزلة لشركتك. يرجى التواصل مع المدير لتنزيلها.';

        if ($companyId) {
            if (!self::isCompanySubscriptionValid($companyId)) {
                $subscriptionInfo = getCompanySubscriptionInfo($companyId);
                if ($subscriptionInfo['CompanyStatus'] === 'Expired') {
                    $reason = 'اشتراك منتهي';
                    $message = 'انتهى اشتراك شركتك. يرجى تجديد الاشتراك للوصول لهذه الوحدة.';
                } elseif ($subscriptionInfo['CompanyStatus'] === 'Trial' &&
                         $subscriptionInfo['trial_end_date'] &&
                         strtotime($subscriptionInfo['trial_end_date']) < time()) {
                    $reason = 'فترة تجريبية منتهية';
                    $message = 'انتهت الفترة التجريبية لشركتك. يرجى الاشتراك في إحدى خططنا للوصول لهذه الوحدة.';
                } elseif ($subscriptionInfo['CompanyStatus'] === 'Inactive') {
                    $reason = 'شركة غير نشطة';
                    $message = 'شركتك غير نشطة حالياً. يرجى التواصل مع الدعم الفني.';
                }
            } elseif (!self::isModuleAllowedInSubscription($moduleCode, $companyId)) {
                $reason = 'وحدة غير مشمولة في الخطة';
                $message = 'هذه الوحدة غير مشمولة في خطة اشتراكك الحالية. يرجى ترقية الخطة للوصول لهذه الوحدة.';
            }
        }

        // إذا كان طلب AJAX
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $message,
                'reason' => $reason
            ]);
            exit;
        }

        // إذا كان طلب عادي - استخدام نفس نمط System
        if (function_exists('flash')) {
            flash('module_error', $message, 'warning');
        }

        // إعادة التوجيه للوحة التحكم
        if (function_exists('redirect')) {
            redirect(base_url('dashboard'));
        }

        // إذا فشلت الدوال المساعدة، استخدم الطريقة المباشرة
        self::renderModuleNotInstalledPage($moduleCode, $reason, $message);
    }

    /**
     * عرض صفحة عدم تنزيل الوحدة (الهيكل الجديد)
     */
    private static function renderModuleNotInstalledPage($moduleCode, $reason = 'وحدة غير منزلة', $message = null)
    {
        http_response_code(403);

        // تنظيف أي مخرجات سابقة
        if (ob_get_level()) {
            ob_clean();
        }

        // تحضير البيانات لصفحة الخطأ
        $error_message = $message ?: 'هذه الوحدة غير متاحة لشركتك';
        $error_type = $reason;
        $show_details = false;

        // عرض صفحة الخطأ
        $errorPagePath = BASE_PATH . '/App/Notifications/errors/403.php';

        if (file_exists($errorPagePath)) {
            include $errorPagePath;
        } else {
            // صفحة خطأ بسيطة إذا لم توجد صفحة الخطأ المخصصة
            echo self::getSimpleModuleNotInstalledPage($moduleCode, $reason, $message);
        }

        exit;
    }

    /**
     * صفحة عدم تنزيل الوحدة البسيطة (الهيكل الجديد)
     */
    private static function getSimpleModuleNotInstalledPage($moduleCode, $reason = 'وحدة غير منزلة', $message = null)
    {
        $message = $message ?: 'هذه الوحدة غير متاحة لشركتك حالياً.';

        // تحديد الأيقونة والألوان حسب السبب
        $icon = '📦';
        $color = '#ffc107';
        $bgColor = '#856404';

        if (strpos($reason, 'اشتراك') !== false) {
            $icon = '💳';
            $color = '#dc3545';
            $bgColor = '#721c24';
        } elseif (strpos($reason, 'تجريبية') !== false) {
            $icon = '⏰';
            $color = '#fd7e14';
            $bgColor = '#7a3e0c';
        } elseif (strpos($reason, 'خطة') !== false) {
            $icon = '⬆️';
            $color = '#6f42c1';
            $bgColor = '#3d1a78';
        }

        return '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <title>' . htmlspecialchars($reason) . '</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .error {
                    background: rgba(255, 255, 255, 0.95);
                    color: ' . $bgColor . ';
                    padding: 40px;
                    border-radius: 20px;
                    max-width: 500px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                }
                .error h1 { color: ' . $color . '; margin-bottom: 20px; }
                .error .reason {
                    background: rgba(0, 0, 0, 0.1);
                    padding: 10px;
                    border-radius: 10px;
                    margin: 20px 0;
                    font-weight: bold;
                }
                .error a {
                    color: #007bff;
                    text-decoration: none;
                    background: #007bff;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    display: inline-block;
                    margin-top: 20px;
                }
                .error a:hover { background: #0056b3; }
                .error .upgrade-btn {
                    background: ' . $color . ';
                    margin-left: 10px;
                }
                .error .upgrade-btn:hover {
                    background: ' . $bgColor . ';
                }
            </style>
        </head>
        <body>
            <div class="error">
                <h1>' . $icon . ' ' . htmlspecialchars($reason) . '</h1>
                <div class="reason">' . htmlspecialchars($reason) . '</div>
                <p>' . htmlspecialchars($message) . '</p>
                <div>
                    <a href="' . (function_exists('base_url') ? base_url('dashboard') : '/') . '">العودة للوحة التحكم</a>';

        if (strpos($reason, 'خطة') !== false || strpos($reason, 'اشتراك') !== false) {
            $upgradeUrl = function_exists('base_url') ? base_url('subscription/upgrade') : '/subscription/upgrade';
            $message .= '<a href="' . $upgradeUrl . '" class="upgrade-btn">ترقية الخطة</a>';
        }

        $message .= '
                </div>
            </div>
        </body>
        </html>';

        return $message;
    }

    /**
     * التحقق من أن المستخدم مضاف في الشركة
     */
    private static function isUserInCompany($userId, $companyId)
    {
        try {
            global $db;
            $stmt = $db->prepare("
                SELECT COUNT(*) as is_member
                FROM company_users
                WHERE user_id = ?
                AND company_id = ?
                AND status = 'accepted'
                AND user_status = 'active'
                AND position_id IS NOT NULL
            ");

            $stmt->execute([$userId, $companyId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result['is_member'] > 0;

        } catch (Exception $e) {
            error_log("خطأ في التحقق من عضوية المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من صلاحية المستخدم
     */
    public static function hasPermission($program, $action = 'view')
    {
        $user = current_user();
        if (!$user) {
            return false;
        }

        $companyId = $user['current_company_id'];
        if (!$companyId) {
            return false;
        }

        // التحقق من أن المستخدم هو مالك الشركة
        if (self::isCompanyOwner($user['UserID'], $companyId)) {
            return true; // مالك الشركة له صلاحيات كاملة
        }

        // تحميل صلاحيات المستخدم إذا لم تكن محملة
        if (self::$userPermissions === null) {
            self::loadUserPermissions();
        }

        // إذا لم تكن هناك صلاحيات محملة
        if (!self::$userPermissions) {
            return false;
        }

        // التحقق من الصلاحية
        $key = $program . '_' . $action;
        return isset(self::$userPermissions[$key]) && self::$userPermissions[$key];
    }

    /**
     * التحقق من أن المستخدم هو مالك الشركة
     */
    public static function isCompanyOwner($userId, $companyId)
    {
        try {
            global $db;

            // التحقق من جدول companies إذا كان المستخدم هو المالك
            $stmt = $db->prepare("
                SELECT COUNT(*) as is_owner
                FROM companies
                WHERE CompanyID = ?
                AND OwnerID = ?
                AND CompanyStatus IN ('Active', 'Trial')
            ");

            $stmt->execute([$companyId, $userId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result['is_owner'] > 0;

        } catch (Exception $e) {
            error_log("خطأ في التحقق من ملكية الشركة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تحميل صلاحيات المستخدم
     */
    private static function loadUserPermissions()
    {
        $user = current_user();
        if (!$user) {
            self::$userPermissions = false;
            return;
        }

        $companyId = $user['current_company_id'];
        $userId = $user['UserID'];

        if (!$companyId) {
            self::$userPermissions = false;
            return;
        }

        try {
            global $db;

            // الحصول على صلاحيات المستخدم من الجداول الفعلية
            $stmt = $db->prepare("
                SELECT
                    prog.name_en as program_name,
                    prog.page_url,
                    perm.CanView,
                    perm.CanCreate,
                    perm.CanEdit,
                    perm.CanDelete,
                    perm.CanApprove,
                    perm.CustomPermissions,
                    pos.PositionNameAR,
                    pos.PositionNameEN
                FROM company_users cu
                JOIN positions pos ON pos.PositionID = cu.position_id
                JOIN permissions perm ON perm.PositionID = pos.PositionID
                JOIN company_all_programs prog ON prog.program_id = perm.ProgramID
                WHERE cu.user_id = ?
                AND cu.company_id = ?
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
                AND cu.position_id IS NOT NULL
                AND perm.CompanyID = ?
                AND prog.company_id = ?
                AND prog.status_en = 'Active'
            ");

            $stmt->execute([$userId, $companyId, $companyId, $companyId]);
            $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // تنظيم الصلاحيات
            self::$userPermissions = [];
            foreach ($permissions as $perm) {
                $program = strtolower($perm['program_name']);

                // الصلاحيات الأساسية
                self::$userPermissions[$program . '_view'] = (bool)$perm['CanView'];
                self::$userPermissions[$program . '_create'] = (bool)$perm['CanCreate'];
                self::$userPermissions[$program . '_edit'] = (bool)$perm['CanEdit'];
                self::$userPermissions[$program . '_delete'] = (bool)$perm['CanDelete'];
                self::$userPermissions[$program . '_approve'] = (bool)$perm['CanApprove'];

                // الصلاحيات المخصصة (JSON)
                if (!empty($perm['CustomPermissions'])) {
                    $customPerms = json_decode($perm['CustomPermissions'], true);
                    if ($customPerms) {
                        foreach ($customPerms as $key => $value) {
                            self::$userPermissions[$program . '_' . $key] = (bool)$value;
                        }
                    }
                }

                // ربط المسارات بالصلاحيات
                if (!empty($perm['page_url'])) {
                    $pageUrl = trim($perm['page_url'], '/');
                    self::$userPermissions['url_' . $pageUrl] = (bool)$perm['CanView'];
                }
            }

        } catch (Exception $e) {
            error_log("خطأ في تحميل الصلاحيات: " . $e->getMessage());
            self::$userPermissions = false;
        }
    }

    /**
     * الحصول على المسار الحالي
     */
    private static function getCurrentRoute()
    {
        $url = $_SERVER['REQUEST_URI'];

        // إزالة المعاملات
        $url = strtok($url, '?');

        // إزالة المسار الأساسي
        $base_path = parse_url(APP_URL, PHP_URL_PATH);
        if ($base_path && strpos($url, $base_path) === 0) {
            $url = substr($url, strlen($base_path));
        }

        // إزالة الشرطة المائلة من البداية والنهاية
        return trim($url, '/');
    }

    /**
     * التعامل مع عدم وجود صلاحية
     */
    private static function handleNoPermission()
    {
        // إذا كان طلب AJAX
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'ليس لديك صلاحية لهذا الإجراء'
            ]);
            exit;
        }

        // إذا كان طلب عادي - استخدام نفس نمط System
        // حفظ رسالة في الجلسة وإعادة التوجيه
        if (function_exists('flash')) {
            flash('permission_error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
        }

        // إعادة التوجيه حسب حالة المستخدم
        $user = current_user();
        if (!$user) {
            // غير مسجل دخول - توجيه لصفحة تسجيل الدخول
            if (function_exists('redirect')) {
                redirect(base_url('login'));
            }
        } elseif (!$user['current_company_id']) {
            // مسجل دخول لكن بدون شركة - توجيه لاختيار الشركة
            if (function_exists('redirect')) {
                redirect(base_url('companies'));
            }
        } else {
            // مسجل دخول مع شركة لكن بدون صلاحية - توجيه للوحة التحكم
            if (function_exists('redirect')) {
                redirect(base_url('dashboard'));
            }
        }

        // إذا فشلت الدوال المساعدة، استخدم الطريقة المباشرة
        self::renderPermissionErrorPage();
    }

    /**
     * عرض صفحة خطأ الصلاحيات مباشرة
     */
    private static function renderPermissionErrorPage()
    {
        http_response_code(403);

        // تنظيف أي مخرجات سابقة
        if (ob_get_level()) {
            ob_clean();
        }

        // تحضير البيانات لصفحة الخطأ
        $error_message = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        $error_type = 'غير مسموح بالوصول';
        $show_details = false;

        // عرض صفحة الخطأ
        $errorPagePath = BASE_PATH . '/App/Notifications/errors/403.php';

        if (file_exists($errorPagePath)) {
            include $errorPagePath;
        } else {
            // صفحة خطأ بسيطة إذا لم توجد صفحة الخطأ المخصصة
            echo self::getSimplePermissionErrorPage();
        }

        exit;
    }

    /**
     * صفحة خطأ صلاحيات بسيطة كبديل
     */
    private static function getSimplePermissionErrorPage()
    {
        return '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <title>غير مسموح بالوصول</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .error {
                    background: rgba(255, 255, 255, 0.95);
                    color: #721c24;
                    padding: 40px;
                    border-radius: 20px;
                    max-width: 500px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                }
                .error h1 { color: #dc3545; margin-bottom: 20px; }
                .error a {
                    color: #007bff;
                    text-decoration: none;
                    background: #007bff;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    display: inline-block;
                    margin-top: 20px;
                }
                .error a:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <div class="error">
                <h1>🔒 غير مسموح بالوصول</h1>
                <p>عذراً، ليس لديك الصلاحية المطلوبة للوصول إلى هذه الصفحة.</p>
                <p>يرجى التواصل مع المدير لمنحك الصلاحيات المطلوبة.</p>
                <a href="' . (function_exists('base_url') ? base_url() : '/') . '">العودة للرئيسية</a>
            </div>
        </body>
        </html>';
    }

    /**
     * تسجيل صلاحية مطلوبة لمسار معين (للاستخدام اليدوي)
     */
    public static function requirePermission($route, $program, $action = 'view')
    {
        self::$routePermissions[$route] = [
            'program' => $program,
            'action' => $action
        ];
    }

    /**
     * الحصول على المسارات المحملة (للتصحيح)
     */
    public static function getLoadedRoutes()
    {
        self::loadProgramRoutes();
        return self::$programRoutes;
    }

    /**
     * الحصول على معلومات منصب المستخدم
     */
    public static function getUserPosition($userId = null, $companyId = null)
    {
        if (!$userId || !$companyId) {
            $user = current_user();
            if (!$user) return null;

            $userId = $userId ?: $user['UserID'];
            $companyId = $companyId ?: $user['current_company_id'];
        }

        try {
            global $db;
            $stmt = $db->prepare("
                SELECT
                    cu.position_id,
                    pos.PositionNameAR,
                    pos.PositionNameEN,
                    cu.status,
                    cu.user_status,
                    cu.join_date
                FROM company_users cu
                JOIN positions pos ON pos.PositionID = cu.position_id
                WHERE cu.user_id = ?
                AND cu.company_id = ?
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
            ");

            $stmt->execute([$userId, $companyId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("خطأ في جلب معلومات المنصب: " . $e->getMessage());
            return null;
        }
    }

    /**
     * التحقق من حالة المستخدم في الشركة
     */
    public static function getUserCompanyStatus($userId = null, $companyId = null)
    {
        if (!$userId || !$companyId) {
            $user = current_user();
            if (!$user) return null;

            $userId = $userId ?: $user['UserID'];
            $companyId = $companyId ?: $user['current_company_id'];
        }

        try {
            global $db;
            $stmt = $db->prepare("
                SELECT
                    status,
                    user_status,
                    position_id,
                    join_date,
                    notes
                FROM company_users
                WHERE user_id = ?
                AND company_id = ?
            ");

            $stmt->execute([$userId, $companyId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("خطأ في جلب حالة المستخدم: " . $e->getMessage());
            return null;
        }
    }
}

/**
 * وظائف مساعدة سريعة
 */
function hasPermission($program, $action = 'view') {
    return PermissionManager::hasPermission($program, $action);
}

function canView($programCode) {
    return checkProgramPermission($programCode, 'view');
}

function canCreate($programCode) {
    return checkProgramPermission($programCode, 'create');
}

function canEdit($programCode) {
    return checkProgramPermission($programCode, 'edit');
}

function canDelete($programCode) {
    return checkProgramPermission($programCode, 'delete');
}

function canApprove($programCode) {
    return checkProgramPermission($programCode, 'approve');
}

function requirePermission($route, $program, $action = 'view') {
    PermissionManager::requirePermission($route, $program, $action);
}

function showIfCan($program, $action, $content) {
    if (PermissionManager::hasPermission($program, $action)) {
        echo $content;
    }
}

/**
 * التحقق من صلاحية مخصصة
 */
function hasCustomPermission($program, $customAction) {
    return PermissionManager::hasPermission($program, $customAction);
}

/**
 * الحصول على جميع صلاحيات المستخدم
 */
function getUserPermissions() {
    return PermissionManager::$userPermissions;
}

/**
 * التحقق من صلاحية المستخدم لبرنامج معين
 */
function canAccessProgram($program) {
    return PermissionManager::hasPermission($program, 'view');
}

/**
 * التحقق من أن المستخدم هو مالك الشركة
 */
function isCompanyOwner($userId = null, $companyId = null) {
    if (!$userId || !$companyId) {
        $user = current_user();
        if (!$user) return false;

        $userId = $userId ?: $user['UserID'];
        $companyId = $companyId ?: $user['current_company_id'];
    }

    return PermissionManager::isCompanyOwner($userId, $companyId);
}

/**
 * الحصول على معلومات منصب المستخدم
 */
function getUserPosition($userId = null, $companyId = null) {
    return PermissionManager::getUserPosition($userId, $companyId);
}

/**
 * الحصول على حالة المستخدم في الشركة
 */
function getUserCompanyStatus($userId = null, $companyId = null) {
    return PermissionManager::getUserCompanyStatus($userId, $companyId);
}

/**
 * التحقق من أن المستخدم نشط في الشركة
 */
function isUserActiveInCompany($userId = null, $companyId = null) {
    $status = getUserCompanyStatus($userId, $companyId);
    return $status &&
           $status['status'] === 'accepted' &&
           $status['user_status'] === 'active' &&
           !empty($status['position_id']);
}

/**
 * التحقق من صحة اشتراك الشركة (دالة عامة)
 */
function isCompanySubscriptionValid($companyId = null) {
    if (!$companyId) {
        $user = current_user();
        if (!$user) return false;
        $companyId = $user['current_company_id'];
    }

    return PermissionManager::isCompanySubscriptionValid($companyId);
}

/**
 * التحقق من أن الوحدة مسموحة في اشتراك الشركة
 */
function isModuleAllowedInSubscription($moduleCode, $companyId = null) {
    if (!$companyId) {
        $user = current_user();
        if (!$user) return false;
        $companyId = $user['current_company_id'];
    }

    // استدعاء الدالة الداخلية مباشرة
    try {
        global $db;

        // الحصول على معلومات الشركة والاشتراك
        $stmt = $db->prepare("
            SELECT
                c.CompanyStatus,
                c.subscription_id,
                s.plan_id
            FROM companies c
            LEFT JOIN subscriptions s ON s.subscription_id = c.subscription_id
            WHERE c.CompanyID = ?
        ");

        $stmt->execute([$companyId]);
        $company = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$company) {
            return false;
        }

        // إذا كانت الشركة في فترة تجريبية، السماح بجميع الوحدات الأساسية والإضافية
        if ($company['CompanyStatus'] === 'Trial') {
            $stmt = $db->prepare("
                SELECT COUNT(*) as count
                FROM system_modules
                WHERE module_code = ?
                AND module_type IN ('core', 'addon')
            ");
            $stmt->execute([$moduleCode]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] > 0;
        }

        // إذا كانت الشركة نشطة، التحقق من نوع الوحدة فقط
        if ($company['CompanyStatus'] === 'Active') {
            // الحصول على نوع الوحدة
            $stmt = $db->prepare("
                SELECT module_type
                FROM system_modules
                WHERE module_code = ?
            ");
            $stmt->execute([$moduleCode]);
            $moduleInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$moduleInfo) {
                return false;
            }

            // جميع الوحدات متاحة للشركات النشطة
            return in_array($moduleInfo['module_type'], ['core', 'addon', 'premium']);
        }

        return false;

    } catch (Exception $e) {
        error_log("خطأ في التحقق من سماح الوحدة في الاشتراك: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على معلومات اشتراك الشركة
 */
function getCompanySubscriptionInfo($companyId = null) {
    if (!$companyId) {
        $user = current_user();
        if (!$user) return null;
        $companyId = $user['current_company_id'];
    }

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT
                c.CompanyStatus,
                c.trial_end_date,
                s.status as subscription_status,
                s.start_date as subscription_start,
                s.end_date as subscription_end,
                sp.plan_name_ar,
                sp.plan_name_en
            FROM companies c
            LEFT JOIN subscriptions s ON s.subscription_id = c.subscription_id
            LEFT JOIN subscription_plans sp ON sp.plan_id = s.plan_id
            WHERE c.CompanyID = ?
        ");

        $stmt->execute([$companyId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("خطأ في جلب معلومات الاشتراك: " . $e->getMessage());
        return null;
    }
}

/**
 * التحقق من أن المستخدم عضو في الشركة (بغض النظر عن الصلاحيات)
 */
function isUserInCompany($userId = null, $companyId = null) {
    if (!$userId || !$companyId) {
        $user = current_user();
        if (!$user) return false;

        $userId = $userId ?: $user['UserID'];
        $companyId = $companyId ?: $user['current_company_id'];
    }

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT COUNT(*) as is_member
            FROM company_users
            WHERE user_id = ?
            AND company_id = ?
            AND status = 'accepted'
            AND user_status = 'active'
            AND position_id IS NOT NULL
        ");

        $stmt->execute([$userId, $companyId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['is_member'] > 0;

    } catch (Exception $e) {
        error_log("خطأ في التحقق من عضوية المستخدم: " . $e->getMessage());
        return false;
    }
}



/**
 * دالة مساعدة للتحقق من صلاحية معينة للبرنامج
 */
function checkProgramPermission($programCode, $action) {
    $user = current_user();
    if (!$user) return false;

    $companyId = $user['current_company_id'];
    if (!$companyId) return false;

    // مالك الشركة له صلاحيات كاملة
    if (isCompanyOwner($user['UserID'], $companyId)) {
        return true;
    }

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT p.*
            FROM permissions p
            JOIN module_programs mp ON mp.program_id = p.program_id
            JOIN company_users cu ON cu.position_id = p.position_id
            WHERE cu.user_id = ?
            AND cu.company_id = ?
            AND cu.status = 'accepted'
            AND cu.user_status = 'active'
            AND mp.program_code = ?
            AND p.company_id = ?
        ");

        $stmt->execute([$user['UserID'], $companyId, $programCode, $companyId]);
        $permission = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$permission) return false;

        switch ($action) {
            case 'view': return (bool)$permission['can_view'];
            case 'create': return (bool)$permission['can_create'];
            case 'edit': return (bool)$permission['can_edit'];
            case 'delete': return (bool)$permission['can_delete'];
            case 'approve': return (bool)$permission['can_approve'];
            default: return false;
        }

    } catch (Exception $e) {
        error_log("خطأ في التحقق من صلاحية البرنامج: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من توفر البرنامج للشركة
 */
function isProgramAvailable($programName, $companyId = null) {
    if (!$companyId) {
        $user = current_user();
        if (!$user) return false;
        $companyId = $user['current_company_id'];
    }

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT COUNT(*) as program_count
            FROM company_all_programs
            WHERE company_id = ?
            AND (name_en = ? OR name_en LIKE ?)
            AND status_en = 'Active'
        ");

        $stmt->execute([$companyId, $programName, $programName . '%']);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['program_count'] > 0;

    } catch (Exception $e) {
        error_log("خطأ في التحقق من توفر البرنامج: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على البرامج الرئيسية للشركة
 */
function getMainPrograms($companyId = null) {
    if (!$companyId) {
        $user = current_user();
        if (!$user) return [];
        $companyId = $user['current_company_id'];
    }

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT
                program_id,
                name_ar,
                name_en,
                page_url,
                icon_name,
                display_order
            FROM company_all_programs
            WHERE company_id = ?
            AND program_type = 'Main'
            AND status_en = 'Active'
            ORDER BY display_order, name_ar
        ");

        $stmt->execute([$companyId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("خطأ في جلب البرامج الرئيسية: " . $e->getMessage());
        return [];
    }
}

/**
 * الحصول على البرامج الفرعية لبرنامج رئيسي
 */
function getSubPrograms($parentProgramId, $companyId = null) {
    if (!$companyId) {
        $user = current_user();
        if (!$user) return [];
        $companyId = $user['current_company_id'];
    }

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT
                program_id,
                name_ar,
                name_en,
                page_url,
                icon_name,
                display_order
            FROM company_all_programs
            WHERE company_id = ?
            AND parent_program_id = ?
            AND program_type = 'Sub'
            AND status_en = 'Active'
            ORDER BY display_order, name_ar
        ");

        $stmt->execute([$companyId, $parentProgramId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("خطأ في جلب البرامج الفرعية: " . $e->getMessage());
        return [];
    }
}

/**
 * الحصول على هيكل البرامج الكامل (رئيسية + فرعية)
 */
function getProgramsHierarchy($companyId = null) {
    $mainPrograms = getMainPrograms($companyId);
    $hierarchy = [];

    foreach ($mainPrograms as $mainProgram) {
        $hierarchy[] = [
            'program' => $mainProgram,
            'sub_programs' => getSubPrograms($mainProgram['program_id'], $companyId)
        ];
    }

    return $hierarchy;
}

/**
 * عرض قائمة البرامج المتاحة للمستخدم
 */
function getAvailablePrograms() {
    $user = current_user();
    if (!$user) return [];

    $companyId = $user['current_company_id'];
    $userId = $user['UserID'];

    if (!$companyId) return [];

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT DISTINCT
                prog.program_id,
                prog.name_ar,
                prog.name_en,
                prog.icon_name,
                prog.page_url,
                prog.program_type,
                prog.parent_program_id,
                prog.display_order
            FROM company_all_programs prog
            JOIN permissions perm ON perm.ProgramID = prog.program_id
            JOIN positions pos ON pos.PositionID = perm.PositionID
            JOIN company_users cu ON cu.position_id = pos.PositionID
            WHERE cu.user_id = ?
            AND cu.company_id = ?
            AND cu.status = 'accepted'
            AND cu.user_status = 'active'
            AND perm.CompanyID = ?
            AND prog.company_id = ?
            AND prog.status_en = 'Active'
            AND perm.CanView = 1
            ORDER BY prog.display_order, prog.name_ar
        ");

        $stmt->execute([$userId, $companyId, $companyId, $companyId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("خطأ في جلب البرامج المتاحة: " . $e->getMessage());
        return [];
    }
}

/**
 * وظائف مساعدة لإدارة المسارات الديناميكية
 */

/**
 * إضافة مسار مخصص (للحالات الخاصة فقط)
 */
function addCustomRoute($route, $program, $action = 'view') {
    PermissionManager::requirePermission($route, $program, $action);
}

/**
 * عرض جميع المسارات المحملة (للتطوير والتصحيح)
 */
function debugRoutes() {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo '<pre>';
        print_r(PermissionManager::getLoadedRoutes());
        echo '</pre>';
    }
}
?>
