<?php
namespace App\System\Users;

use App\Core\Module as BaseModule;

/**
 * وحدة المستخدمين
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // تسجيل مسارات المستخدمين (معطلة مؤقتاً - يمكن تفعيلها عند الحاجة)
        // add_route('GET', '/users', 'App\System\Users\Controllers\UserController@index');
        // add_route('GET', '/users/create', 'App\System\Users\Controllers\UserController@create');
        // add_route('POST', '/users/store', 'App\System\Users\Controllers\UserController@store');
        // add_route('GET', '/users/{id}', 'App\System\Users\Controllers\UserController@show');
        // add_route('GET', '/users/{id}/edit', 'App\System\Users\Controllers\UserController@edit');
        // add_route('POST', '/users/{id}/update', 'App\System\Users\Controllers\UserController@update');
        // add_route('POST', '/users/{id}/delete', 'App\System\Users\Controllers\UserController@delete');

        // تسجيل مسارات الملف الشخصي
        add_route('GET', '/profile', 'App\System\Users\Controllers\ProfileController@profile');
        add_route('POST', '/profile', 'App\System\Users\Controllers\ProfileController@profile');
        add_route('POST', '/profile/update-picture', 'App\System\Users\Controllers\ProfileController@updatePicture');

        // تسجيل مسارات الإعدادات المحددة أولاً
        add_route('GET', '/settings/theme/{theme}', 'App\System\Users\Controllers\SettingsController@theme');
        add_route('GET', '/settings/sidebar/{mode}', 'App\System\Users\Controllers\SettingsController@sidebar');
        add_route('GET', '/settings/content/{mode}', 'App\System\Users\Controllers\SettingsController@content');
        add_route('GET', '/settings/sound-notifications/{mode}', 'App\System\Users\Controllers\SettingsController@soundNotifications');
        add_route('GET', '/settings', 'App\System\Users\Controllers\SettingsController@index');
    }
}
