[2025-05-29 00:00:32] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('35')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-29 01:06:31] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('35')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-29 01:06:53] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('35')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-29 01:07:07] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('35')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-29 01:10:32] Exception: Controller class App\Modules\Inventory\Controllers\CategoryController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 01:10:54] Exception: Controller class App\Modules\Inventory\Controllers\UnitController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 12:47:34] Exception: Controller class App\Modules\Inventory\Controllers\UnitController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 14:15:53] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('32')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-29 14:28:08] Exception: Controller class App\Modules\Inventory\Controllers\CategoryController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 14:28:12] Exception: Controller class App\Modules\Inventory\Controllers\UnitController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 14:28:15] Exception: Controller class App\Modules\Inventory\Controllers\WarehouseController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 16:19:57] Exception: Controller class App\Controllers\App\Modules\Dashboard\Controllers\ModulesDashboardController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/App\Modules\Dashboard\Controllers\ModulesDashboardController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(88): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 18:00:59] Exception: Controller class App\Controllers\DashboardController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/DashboardController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(88): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 18:01:22] Exception: Controller class App\Controllers\DashboardController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/DashboardController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(88): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:14:41] ErrorException: Cannot declare class App\System\Dashboard\Module, because the name is already in use in /home1/qqoshqmy/public_html/erpapp/App/Modules/Dashboard/Module.php:9
Stack trace:
#0 [internal function]: App\Core\ExceptionHandler::handleShutdown()
#1 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:14:44] ErrorException: Cannot declare class App\System\Dashboard\Module, because the name is already in use in /home1/qqoshqmy/public_html/erpapp/App/Modules/Dashboard/Module.php:9
Stack trace:
#0 [internal function]: App\Core\ExceptionHandler::handleShutdown()
#1 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:21:49] ErrorException: Cannot declare class App\System\Dashboard\Module, because the name is already in use in /home1/qqoshqmy/public_html/erpapp/App/Modules/Dashboard/Module.php:9
Stack trace:
#0 [internal function]: App\Core\ExceptionHandler::handleShutdown()
#1 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:23:31] ErrorException: Cannot declare class App\System\Dashboard\Module, because the name is already in use in /home1/qqoshqmy/public_html/erpapp/App/Modules/Dashboard/Module.php:9
Stack trace:
#0 [internal function]: App\Core\ExceptionHandler::handleShutdown()
#1 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:24:04] ErrorException: Cannot declare class App\System\Dashboard\Module, because the name is already in use in /home1/qqoshqmy/public_html/erpapp/App/Modules/Dashboard/Module.php:9
Stack trace:
#0 [internal function]: App\Core\ExceptionHandler::handleShutdown()
#1 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:40:11] Exception: Controller class App\Controllers\DashboardController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/DashboardController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(88): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:40:44] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:40:47] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:41:06] Error: Class "App\Modules\home\Services\DashboardService" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/home/<USER>/DashboardController.php:29
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\home\Controllers\DashboardController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-29 19:56:19] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:01:11] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:02:01] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:02:03] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:04:33] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:05:26] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:06:26] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:07:38] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:07:41] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 20:07:42] Exception: Controller class App\Modules\home\Controllers\DashboardController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-29 22:06:39] Error: Class "PermissionManager" not found in /home1/qqoshqmy/public_html/erpapp/loader.php:80
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once()
#1 {main}
--------------------------------------------------------------------------------
[2025-05-29 22:06:42] Error: Class "PermissionManager" not found in /home1/qqoshqmy/public_html/erpapp/loader.php:80
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once()
#1 {main}
--------------------------------------------------------------------------------
[2025-05-29 22:08:06] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('35')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
