<?php
/**
 * اختبار نظام صلاحيات مجلد System
 * 
 * هذا الملف يختبر أن النظام يتعرف على جميع مجلدات System تلقائياً
 */

// تحميل الإعدادات
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/App/Helpers/permissions.php';

echo "🧪 اختبار نظام صلاحيات مجلد System\n";
echo "=====================================\n\n";

// استخدام reflection للوصول للدوال الخاصة
$reflection = new ReflectionClass('App\Helpers\Permissions');
$getSystemModulesMethod = $reflection->getMethod('getSystemModules');
$getSystemModulesMethod->setAccessible(true);
$isSystemRouteMethod = $reflection->getMethod('isSystemRoute');
$isSystemRouteMethod->setAccessible(true);

// الحصول على قائمة وحدات System
$systemModules = $getSystemModulesMethod->invoke(null);

echo "📁 وحدات System المكتشفة:\n";
echo "-------------------------\n";
foreach ($systemModules as $module) {
    echo "✅ " . $module . "\n";
}
echo "\n";

// اختبار مسارات مختلفة
$testRoutes = [
    // مسارات يجب أن تكون مسموحة (من System)
    'companies',
    'companies/create',
    'companies/123/edit',
    'users',
    'users/profile',
    'profile',
    'profile/edit',
    'settings',
    'settings/theme',
    'dashboard',
    'logout',
    'language/ar',
    'lang/en',
    
    // مسارات يجب أن تكون ممنوعة (من Modules)
    'inventory',
    'inventory/items',
    'sales',
    'sales/invoices',
    'accounting',
    'accounting/reports',
    'hr',
    'hr/employees',
];

echo "🔍 اختبار المسارات:\n";
echo "-------------------\n";

$allowedCount = 0;
$deniedCount = 0;

foreach ($testRoutes as $route) {
    $isAllowed = $isSystemRouteMethod->invoke(null, $route);
    
    if ($isAllowed) {
        echo "✅ مسموح: /" . $route . "\n";
        $allowedCount++;
    } else {
        echo "❌ ممنوع: /" . $route . "\n";
        $deniedCount++;
    }
}

echo "\n📊 ملخص النتائج:\n";
echo "----------------\n";
echo "✅ مسارات مسموحة: " . $allowedCount . "\n";
echo "❌ مسارات ممنوعة: " . $deniedCount . "\n";
echo "📝 إجمالي المسارات المختبرة: " . count($testRoutes) . "\n\n";

// فحص مجلدات System الفعلية
$systemPath = BASE_PATH . '/App/System';
echo "📂 فحص مجلدات System الفعلية:\n";
echo "-------------------------------\n";

if (is_dir($systemPath)) {
    $actualDirectories = scandir($systemPath);
    $actualSystemDirs = [];
    
    foreach ($actualDirectories as $dir) {
        if ($dir !== '.' && $dir !== '..' && is_dir($systemPath . '/' . $dir)) {
            $actualSystemDirs[] = strtolower($dir);
            echo "📁 " . $dir . "\n";
        }
    }
    
    echo "\n🔄 مقارنة مع القائمة المكتشفة:\n";
    echo "--------------------------------\n";
    
    $detectedSystemDirs = array_filter($systemModules, function($module) {
        return !in_array($module, ['logout', 'language', 'lang']);
    });
    
    $missing = array_diff($actualSystemDirs, $detectedSystemDirs);
    $extra = array_diff($detectedSystemDirs, $actualSystemDirs);
    
    if (empty($missing) && empty($extra)) {
        echo "✅ جميع المجلدات مكتشفة بشكل صحيح!\n";
    } else {
        if (!empty($missing)) {
            echo "⚠️  مجلدات مفقودة من القائمة:\n";
            foreach ($missing as $dir) {
                echo "   - " . $dir . "\n";
            }
        }
        
        if (!empty($extra)) {
            echo "⚠️  مجلدات إضافية في القائمة:\n";
            foreach ($extra as $dir) {
                echo "   - " . $dir . "\n";
            }
        }
    }
} else {
    echo "❌ مجلد System غير موجود: " . $systemPath . "\n";
}

echo "\n🎯 اختبار حالات خاصة:\n";
echo "---------------------\n";

// اختبار حالات خاصة
$specialCases = [
    'COMPANIES' => 'companies',  // حالة كبيرة
    'Companies/Create' => 'companies/create',  // حالة مختلطة
    'users/PROFILE' => 'users/profile',  // حالة مختلطة
    '' => '',  // مسار فارغ
    '/' => '/',  // مسار جذر
];

foreach ($specialCases as $testCase => $expected) {
    $isAllowed = $isSystemRouteMethod->invoke(null, $testCase);
    $expectedResult = $isSystemRouteMethod->invoke(null, $expected);
    
    if ($isAllowed === $expectedResult) {
        echo "✅ " . ($testCase ?: '(فارغ)') . " → " . ($isAllowed ? 'مسموح' : 'ممنوع') . "\n";
    } else {
        echo "❌ " . ($testCase ?: '(فارغ)') . " → نتيجة غير متوقعة\n";
    }
}

echo "\n🏁 انتهى الاختبار!\n";
echo "==================\n";

// نصائح للمطور
echo "\n💡 نصائح:\n";
echo "---------\n";
echo "• لإضافة وحدة جديدة: أنشئ مجلد في App/System/\n";
echo "• النظام سيكتشف الوحدة الجديدة تلقائياً\n";
echo "• لا حاجة لتعديل أي كود إضافي\n";
echo "• جميع المسارات في الوحدة ستكون مسموحة\n";

?>
