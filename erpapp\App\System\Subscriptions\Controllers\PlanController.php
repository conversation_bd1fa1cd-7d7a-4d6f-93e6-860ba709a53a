<?php
namespace App\System\Subscriptions\Controllers;

use App\System\Subscriptions\Models\SubscriptionPlan;

/**
 * متحكم خطط الاشتراك
 */
class PlanController {
    /**
     * معلمات المسار
     *
     * @var array
     */
    protected $params = [];

    /**
     * نموذج خطة الاشتراك
     *
     * @var SubscriptionPlan
     */
    protected $planModel;

    /**
     * المُنشئ
     *
     * @param array $params معلمات المسار
     */
    public function __construct($params = []) {
        $this->params = $params;
        $this->planModel = new SubscriptionPlan();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض قائمة خطط الاشتراك
     *
     * @return void
     */
    public function index() {
        // الحصول على جميع خطط الاشتراك
        $plans = $this->planModel->getAllActivePlans();

        view('Subscriptions::admin/plans/index', [
            'title' => __('إدارة خطط الاشتراك'),
            'plans' => $plans
        ]);
    }

    /**
     * عرض نموذج إنشاء خطة اشتراك جديدة
     *
     * @return void
     */
    public function create() {
        view('Subscriptions::admin/plans/create', [
            'title' => __('إنشاء خطة اشتراك جديدة')
        ]);
    }

    /**
     * حفظ خطة اشتراك جديدة
     *
     * @return void
     */
    public function store() {
        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('admin/plans/create'));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('plan_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('admin/plans/create'));
        }

        // التحقق من البيانات المدخلة
        $plan_name_ar = trim($_POST['plan_name_ar'] ?? '');
        $plan_name_en = trim($_POST['plan_name_en'] ?? '');
        $description_ar = trim($_POST['description_ar'] ?? '');
        $description_en = trim($_POST['description_en'] ?? '');
        $price_monthly = floatval($_POST['price_monthly'] ?? 0);
        $price_yearly = floatval($_POST['price_yearly'] ?? 0);
        $max_users = intval($_POST['max_users'] ?? 5);
        $max_programs = intval($_POST['max_programs'] ?? 3);
        $max_storage_gb = intval($_POST['max_storage_gb'] ?? 5);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        if (empty($plan_name_ar) || empty($plan_name_en)) {
            flash('plan_error', 'يرجى إدخال اسم الخطة بالعربية والإنجليزية', 'danger');
            redirect(base_url('admin/plans/create'));
        }

        if ($price_monthly < 0 || $price_yearly < 0) {
            flash('plan_error', 'يجب أن تكون الأسعار قيمة موجبة', 'danger');
            redirect(base_url('admin/plans/create'));
        }

        // إعداد بيانات الخطة
        $plan_data = [
            'plan_name_ar' => $plan_name_ar,
            'plan_name_en' => $plan_name_en,
            'description_ar' => $description_ar,
            'description_en' => $description_en,
            'price_monthly' => $price_monthly,
            'price_yearly' => $price_yearly,
            'max_users' => $max_users,
            'max_programs' => $max_programs,
            'max_storage_gb' => $max_storage_gb,
            'is_active' => $is_active
        ];

        // إنشاء الخطة
        $plan_id = $this->planModel->createPlan($plan_data);

        if ($plan_id) {
            flash('plan_success', 'تم إنشاء خطة الاشتراك بنجاح', 'success');
            redirect(base_url('admin/plans'));
        } else {
            flash('plan_error', 'حدث خطأ أثناء إنشاء خطة الاشتراك. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('admin/plans/create'));
        }
    }

    /**
     * عرض نموذج تعديل خطة اشتراك
     *
     * @return void
     */
    public function edit() {
        $plan_id = $this->params['id'] ?? null;

        if (!$plan_id) {
            flash('plan_error', 'خطة الاشتراك غير موجودة', 'danger');
            redirect(base_url('admin/plans'));
        }

        // الحصول على تفاصيل خطة الاشتراك
        $plan = $this->planModel->getPlanById($plan_id);

        if (!$plan) {
            flash('plan_error', 'خطة الاشتراك غير موجودة', 'danger');
            redirect(base_url('admin/plans'));
        }

        view('Subscriptions::admin/plans/edit', [
            'title' => __('تعديل خطة اشتراك'),
            'plan' => $plan
        ]);
    }

    /**
     * تحديث خطة اشتراك
     *
     * @return void
     */
    public function update() {
        $plan_id = $this->params['id'] ?? null;

        if (!$plan_id) {
            flash('plan_error', 'خطة الاشتراك غير موجودة', 'danger');
            redirect(base_url('admin/plans'));
        }

        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('admin/plans/' . $plan_id . '/edit'));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('plan_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('admin/plans/' . $plan_id . '/edit'));
        }

        // التحقق من البيانات المدخلة
        $plan_name_ar = trim($_POST['plan_name_ar'] ?? '');
        $plan_name_en = trim($_POST['plan_name_en'] ?? '');
        $description_ar = trim($_POST['description_ar'] ?? '');
        $description_en = trim($_POST['description_en'] ?? '');
        $price_monthly = floatval($_POST['price_monthly'] ?? 0);
        $price_yearly = floatval($_POST['price_yearly'] ?? 0);
        $max_users = intval($_POST['max_users'] ?? 5);
        $max_programs = intval($_POST['max_programs'] ?? 3);
        $max_storage_gb = intval($_POST['max_storage_gb'] ?? 5);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        if (empty($plan_name_ar) || empty($plan_name_en)) {
            flash('plan_error', 'يرجى إدخال اسم الخطة بالعربية والإنجليزية', 'danger');
            redirect(base_url('admin/plans/' . $plan_id . '/edit'));
        }

        if ($price_monthly < 0 || $price_yearly < 0) {
            flash('plan_error', 'يجب أن تكون الأسعار قيمة موجبة', 'danger');
            redirect(base_url('admin/plans/' . $plan_id . '/edit'));
        }

        // إعداد بيانات الخطة
        $plan_data = [
            'plan_name_ar' => $plan_name_ar,
            'plan_name_en' => $plan_name_en,
            'description_ar' => $description_ar,
            'description_en' => $description_en,
            'price_monthly' => $price_monthly,
            'price_yearly' => $price_yearly,
            'max_users' => $max_users,
            'max_programs' => $max_programs,
            'max_storage_gb' => $max_storage_gb,
            'is_active' => $is_active
        ];

        // تحديث الخطة
        if ($this->planModel->updatePlan($plan_id, $plan_data)) {
            flash('plan_success', 'تم تحديث خطة الاشتراك بنجاح', 'success');
            redirect(base_url('admin/plans'));
        } else {
            flash('plan_error', 'حدث خطأ أثناء تحديث خطة الاشتراك. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('admin/plans/' . $plan_id . '/edit'));
        }
    }
}
