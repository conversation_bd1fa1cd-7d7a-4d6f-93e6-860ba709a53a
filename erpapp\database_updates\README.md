# تحديثات قاعدة البيانات

هذا المجلد يحتوي على التحديثات المطلوبة لقاعدة البيانات لحل مشكلة العمود المفقود `status_ar`.

## المشكلة

كان التطبيق يحاول الوصول لعمود `status_ar` في جدول `company_users` لكن هذا العمود غير موجود في قاعدة البيانات الحالية، مما يسبب الخطأ:

```
SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list'
```

## الحل

تم إنشاء ملفات التحديث التالية:

### 1. `add_status_ar_column.sql`
- يضيف عمود `status_ar` إلى جدول `company_users`
- يحدث البيانات الموجودة لتتطابق مع عمود `status`
- ينشئ triggers تلقائية لضمان تزامن العمودين

### 2. `run_update.php`
- ملف PHP لتنفيذ التحديث بأمان
- يتحقق من وجود العمود قبل الإضافة
- يعرض تقرير مفصل عن التحديث

## كيفية التشغيل

### الطريقة الأولى: عبر سطر الأوامر
```bash
cd /path/to/erpapp/database_updates
php run_update.php
```

### الطريقة الثانية: عبر المتصفح
1. ارفع الملفات إلى الخادم
2. اذهب إلى: `http://yoursite.com/erpapp/database_updates/run_update.php`

### الطريقة الثالثة: عبر phpMyAdmin
1. افتح phpMyAdmin
2. اختر قاعدة البيانات
3. اذهب إلى تبويب SQL
4. انسخ محتوى ملف `add_status_ar_column.sql` والصقه
5. اضغط تنفيذ

## التحقق من نجاح التحديث

بعد تشغيل التحديث، يجب أن ترى:

1. ✅ رسالة نجاح التحديث
2. ✅ وجود عمود `status_ar` في جدول `company_users`
3. ✅ قيم صحيحة في العمود الجديد
4. ✅ اختفاء رسائل الخطأ من التطبيق

## هيكل العمود الجديد

```sql
status_ar enum('قيد الانتظار','مقبول','مرفوض') 
CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci 
NOT NULL DEFAULT 'قيد الانتظار'
```

## التطابق بين العمودين

| status | status_ar |
|--------|-----------|
| pending | قيد الانتظار |
| accepted | مقبول |
| rejected | مرفوض |

## الـ Triggers المضافة

### 1. `update_status_ar_trigger`
- يتم تشغيله عند تحديث `status`
- يحدث `status_ar` تلقائياً

### 2. `insert_status_ar_trigger`
- يتم تشغيله عند إدراج سجل جديد
- يضبط `status_ar` حسب قيمة `status`

## ملاحظات مهمة

1. **النسخ الاحتياطي**: تأكد من عمل نسخة احتياطية قبل التحديث
2. **الأمان**: التحديث آمن ولا يؤثر على البيانات الموجودة
3. **التوافق**: يعمل مع جميع إصدارات MySQL 5.7+
4. **الأداء**: التحديث سريع حتى مع البيانات الكبيرة

## استكشاف الأخطاء

### إذا فشل التحديث:
1. تحقق من صلاحيات قاعدة البيانات
2. تأكد من إصدار MySQL
3. راجع رسائل الخطأ في الملف

### إذا استمر الخطأ:
1. تحقق من اتصال قاعدة البيانات
2. تأكد من تحديث ملفات التطبيق
3. امسح cache التطبيق إن وجد

## الدعم

في حالة وجود مشاكل، يرجى:
1. التحقق من سجلات الأخطاء
2. التأكد من تطبيق جميع التحديثات
3. إعادة تشغيل خادم الويب
